#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, render_template, session
import json
from ..knowledge_manager import KnowledgeManager

def create_knowledge_blueprint(knowledge_manager:KnowledgeManager):
    """创建知识库路由蓝图"""
    bp = Blueprint('knowledge', __name__, url_prefix='/aitest/knowledge')
    
    @bp.route('/')
    def index():
        """知识库管理页面"""
        return render_template('knowledge/index.html')
    
    @bp.route('/api/list')
    def list_knowledge_documents():
        """获取知识库文档列表API"""
        #print(f"查询知识库文档列表")
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        try:
            documents = knowledge_manager.list_knowledge_documents(project_id)
            return jsonify({
                'success': True,
                'data': documents
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取知识库文档列表失败: {str(e)}'
            }), 500
    
    @bp.route('/api/upload', methods=['POST'])
    def upload_knowledge_file():
        """上传知识库文件API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400
        
        file = request.files['file']
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        file_type = request.form.get('file_type', 'knowledge')
        
        try:
            document_id = knowledge_manager.upload_knowledge_file(
                project_id=project_id,
                file=file,
                name=name,
                description=description,
                file_type=file_type
            )
            
            return jsonify({
                'success': True,
                'data': {'document_id': document_id},
                'message': '知识库文件上传成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'知识库文件上传失败: {str(e)}'
            }), 500
    
    @bp.route('/api/search', methods=['POST'])
    def search_knowledge_base():
        """搜索知识库API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        query = data.get('query', '').strip()
        top_k = data.get('top_k', 5)
        
        if not query:
            return jsonify({
                'success': False,
                'message': '请输入搜索关键词'
            }), 400
        
        try:
            results = knowledge_manager.search_knowledge_base(project_id, query, top_k)
            return jsonify({
                'success': True,
                'data': results
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'搜索知识库失败: {str(e)}'
            }), 500
    
    @bp.route('/api/test', methods=['POST'])
    def test_knowledge_base():
        """测试知识库API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        test_queries = data.get('test_queries', [])
        
        if not test_queries:
            # 使用默认测试查询
            test_queries = [
                '系统登录功能',
                '用户权限管理',
                '数据备份',
                '安全要求',
                '性能指标'
            ]
        
        try:
            results = knowledge_manager.test_knowledge_base(project_id, test_queries)
            return jsonify({
                'success': True,
                'data': results
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'测试知识库失败: {str(e)}'
            }), 500
    
    @bp.route('/api/stats')
    def get_knowledge_base_stats():
        """获取知识库统计信息API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        try:
            stats = knowledge_manager.get_knowledge_base_stats(project_id)
            return jsonify({
                'success': True,
                'data': stats
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取知识库统计失败: {str(e)}'
            }), 500
    
    @bp.route('/api/auto_import', methods=['POST'])
    def auto_import_documents():
        """自动导入文档到知识库API"""
        project_id = session.get('current_project_id')
        print(f"****auto import project[{project_id}]'s documents to knowledge base")
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        try:
            result = knowledge_manager.auto_import_documents(project_id)
            return jsonify({
                'success': True,
                'data': result,
                'message': f'成功导入 {result["success_count"]} 个文档'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'自动导入文档失败: {str(e)}'
            }), 500
    
    @bp.route('/api/remove_document', methods=['POST'])
    def remove_document_from_knowledge_base():
        """从知识库移除文档API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        document_id = data.get('document_id')
        
        if not document_id:
            return jsonify({
                'success': False,
                'message': '请指定要移除的文档'
            }), 400
        
        try:
            success = knowledge_manager.remove_document_from_knowledge_base(project_id, document_id)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '文档已从知识库移除'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '移除文档失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'移除文档失败: {str(e)}'
            }), 500
    
    @bp.route('/api/clear', methods=['POST'])
    def clear_knowledge_base():
        """清空知识库API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        try:
            success = knowledge_manager.clear_knowledge_base(project_id)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '知识库已清空'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '清空知识库失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'清空知识库失败: {str(e)}'
            }), 500
    
    @bp.route('/api/test_embedding', methods=['POST'])
    def test_embedding_service():
        """测试嵌入服务API"""
        try:
            from ..utils.embedding_client import EmbeddingClient
            embedding_client = EmbeddingClient(knowledge_manager.config)
            result = embedding_client.test_embedding_service()
            
            return jsonify({
                'success': result['success'],
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'测试嵌入服务失败: {str(e)}'
            }), 500
    
    return bp
