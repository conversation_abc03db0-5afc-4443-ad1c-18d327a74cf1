#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from flask import Flask, render_template, request, jsonify, session, redirect, url_for, send_from_directory
from flask_cors import CORS
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 导入模块
from src.config import Config
from src.storage import StorageManager
from src.project_manager import ProjectManager
from src.document_manager import DocumentManager
from src.requirement_manager import RequirementManager
from src.knowledge_manager import KnowledgeManager
from src.test_manager import TestManager
from src.system_config import SystemConfigManager

def create_app():
    app = Flask(__name__)
    app.secret_key = os.urandom(24)
    
    # 启用CORS
    CORS(app)
    
    # 初始化配置
    config = Config()
    app.config.update(config.get_all())
    
    # 初始化存储管理器
    storage = StorageManager(config)
    
    # 初始化各个管理器
    project_manager = ProjectManager(storage)
    document_manager = DocumentManager(storage, config)
    requirement_manager = RequirementManager(storage, config)
    knowledge_manager = KnowledgeManager(storage, config)
    test_manager = TestManager(storage, config)
    system_config_manager = SystemConfigManager(storage, config)
    
    # 注册蓝图
    from src.routes.project_routes import create_project_blueprint
    from src.routes.document_routes import create_document_blueprint
    from src.routes.requirement_routes import create_requirement_blueprint
    from src.routes.knowledge_routes import create_knowledge_blueprint
    from src.routes.test_routes import create_test_blueprint
    from src.routes.system_routes import create_system_blueprint
    
    app.register_blueprint(create_project_blueprint(project_manager))
    app.register_blueprint(create_document_blueprint(document_manager))
    app.register_blueprint(create_requirement_blueprint(requirement_manager))
    app.register_blueprint(create_knowledge_blueprint(knowledge_manager))
    app.register_blueprint(create_test_blueprint(test_manager))
    app.register_blueprint(create_system_blueprint(system_config_manager))
    
    @app.route('/aitest/')
    def index():
        """主页"""
        projects = project_manager.list_projects()
        return render_template('index.html', projects=projects)
    
    @app.route('/aitest/api/current_project')
    def get_current_project():
        """获取当前选中的项目"""
        return jsonify({'project_id': session.get('current_project_id')})
    
    @app.route('/aitest/api/set_current_project', methods=['POST'])
    def set_current_project():
        """设置当前项目"""
        if not request.is_json:
            return jsonify({'success': False, 'error': 'Missing JSON in request'}), 400

        try:
            data = request.get_json()
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 400

        project_id = data.get('project_id')
        if project_id is None:
            return jsonify({'success': False, 'error': 'Missing project_id in JSON'}), 400

        session['current_project_id'] = project_id
        return jsonify({'success': True})
    # 静态文件路由 - 新增的带前缀访问
    @app.route('/aitest/static/<path:filename>')
    def custom_static(filename):
        return send_from_directory('static', filename)
    
    return app


if __name__ == '__main__':
    app = create_app()
    app.run(
        host=os.getenv('APP_HOST', '0.0.0.0'),
        port=int(os.getenv('APP_PORT', 5000)),
        debug=os.getenv('APP_DEBUG', 'True').lower() == 'true'
    )
