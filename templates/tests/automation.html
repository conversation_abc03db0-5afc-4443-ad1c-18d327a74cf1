{% extends "base.html" %}

{% block title %}自动化测试用例 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/aitest">首页</a></li>
<li class="breadcrumb-item active">自动化测试用例</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-robot me-2"></i>自动化测试用例
            </h1>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="showGenerateModal()">
                    <i class="fas fa-magic me-1"></i>生成自动化测试
                </button>
                <button class="btn btn-success" onclick="showCreateModal()">
                    <i class="fas fa-plus me-1"></i>创建自动化测试
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤器 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="搜索自动化测试用例...">
        </div>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="statusFilter">
            <option value="">全部状态</option>
            <option value="draft">草稿</option>
            <option value="ready">就绪</option>
            <option value="running">运行中</option>
            <option value="passed">通过</option>
            <option value="failed">失败</option>
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="typeFilter">
            <option value="">全部类型</option>
            <option value="unit">单元测试</option>
            <option value="integration">集成测试</option>
            <option value="api">API测试</option>
            <option value="ui">UI测试</option>
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="pageSizeSelect" onchange="changePageSize()">
            <option value="10" selected>10条</option>
            <option value="30">30条</option>
            <option value="100">100条</option>
        </select>
    </div>
    <div class="col-md-2 text-end">
        <button class="btn btn-outline-secondary" onclick="refreshTests()">
            <i class="fas fa-sync me-1"></i>刷新
        </button>
    </div>
</div>

<!-- 自动化测试用例列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <div id="batchActions" style="display: none;">
                <button class="btn btn-danger" onclick="batchDelete()">
                    <i class="fas fa-trash me-1"></i>批量删除
                </button>
                <button class="btn btn-success" onclick="batchExecute()">
                    <i class="fas fa-play me-1"></i>批量执行
                </button>
            </div>
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="headerCheckbox" onchange="toggleSelectAll()">
                        </th>
                        <th>测试用例名称</th>
                        <th>框架</th>
                        <th>状态</th>
                        <th>关联用户用例</th>
                        <th>最后执行</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="automationTestsTableBody">
                    <!-- 自动化测试用例列表将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页 -->
<div class="d-flex justify-content-between align-items-center mt-4">
    <nav>
        <ul class="pagination" id="pagination">
            <!-- 分页将通过JavaScript动态生成 -->
        </ul>
    </nav>
</div>

<!-- 生成自动化测试模态框 -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">生成自动化测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="requirementSelect" class="form-label">选择需求功能点</label>
                        <select class="form-select" id="requirementSelect" multiple size="8">
                            <!-- 需求选项将通过JavaScript动态加载 -->
                        </select>
                        <div class="form-text">按住Ctrl键可多选</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="automationTestType" class="form-label">自动化测试类型</label>
                                <select class="form-select" id="automationTestType">
                                    <option value="unit">单元测试</option>
                                    <option value="integration">集成测试</option>
                                    <option value="api">API测试</option>
                                    <option value="ui">UI测试</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="generateThreads" class="form-label">并发线程数</label>
                                <input type="number" class="form-control" id="generateThreads" min="1" max="10" value="4">
                                <div class="form-text">用于并发生成测试用例</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeSetup" checked>
                            <label class="form-check-label" for="includeSetup">
                                生成测试环境设置代码
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeTeardown" checked>
                            <label class="form-check-label" for="includeTeardown">
                                生成测试清理代码
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="generateAutomationTests()">开始生成</button>
            </div>
        </div>
    </div>
</div>

<!-- 创建自动化测试用例模态框 -->
<div class="modal fade" id="createModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建自动化测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createForm">
                    <div class="mb-3">
                        <label for="createName" class="form-label">测试用例名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="createName" required>
                    </div>
                    <div class="mb-3">
                        <label for="createDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="createDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="createFramework" class="form-label">测试框架</label>
                                <select class="form-select" id="createFramework">
                                    <option value="midscenejs">Midscene.js</option>
                                    <option value="selenium">Selenium</option>
                                    <option value="playwright">Playwright</option>
                                    <option value="cypress">Cypress</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="createUserTestCase" class="form-label">关联用户测试用例</label>
                                <select class="form-select" id="createUserTestCase">
                                    <option value="">无关联用例</option>
                                    <!-- 用户测试用例选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="createScript" class="form-label">测试脚本 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="createScript" rows="10" placeholder="请输入YAML格式的测试脚本..." required></textarea>
                        <div class="form-text">请使用YAML格式编写测试脚本</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createAutomationTest()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑自动化测试用例模态框 -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑自动化测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="editId">
                    <div class="mb-3">
                        <label for="editName" class="form-label">测试用例名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="editDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editFramework" class="form-label">测试框架</label>
                                <select class="form-select" id="editFramework">
                                    <option value="midscenejs">Midscene.js</option>
                                    <option value="selenium">Selenium</option>
                                    <option value="playwright">Playwright</option>
                                    <option value="cypress">Cypress</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editUserTestCase" class="form-label">关联用户测试用例</label>
                                <select class="form-select" id="editUserTestCase">
                                    <option value="">无关联用例</option>
                                    <!-- 用户测试用例选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editScript" class="form-label">测试脚本 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="editScript" rows="10" required></textarea>
                        <div class="form-text">请使用YAML格式编写测试脚本</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateAutomationTest()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentSearch = '';
let currentStatusFilter = '';
let currentTypeFilter = '';
let selectedTests = new Set();

$(document).ready(function() {
    loadAutomationTests();
    loadRequirements();
    loadUserTestCases();

    // 搜索输入框事件
    $('#searchInput').on('input', debounce(function() {
        currentSearch = $(this).val();
        currentPage = 1;
        loadAutomationTests();
    }, 500));

    // 过滤器事件
    $('#statusFilter, #typeFilter').change(function() {
        currentPage = 1;
        loadAutomationTests();
    });

    // 当显示创建或编辑模态框时，重新加载用户测试用例选项
    $('#createModal, #editModal').on('show.bs.modal', function() {
        loadUserTestCases();
    });
});

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 加载自动化测试用例列表
function loadAutomationTests() {
    const pageSize = parseInt($('#pageSizeSelect').val()) || 10;
    const params = {
        page: currentPage,
        page_size: pageSize,
        automation_only: true  // 只获取自动化测试用例
    };

    if (currentSearch) params.search = currentSearch;
    if ($('#statusFilter').val()) params.status = $('#statusFilter').val();
    if ($('#typeFilter').val()) params.type = $('#typeFilter').val();

    $.get('/aitest/tests/api/automation/list', params, function(response) {
        if (response.success) {
            renderAutomationTestsTable(response.data.records);
            initPagination('pagination', response.data.total_pages, currentPage, function(page) {
                currentPage = page;
                loadAutomationTests();
            });
        } else {
            showNotification('加载自动化测试用例失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载自动化测试用例失败', 'error');
    });
}

// 渲染自动化测试用例表格
function renderAutomationTestsTable(tests) {
    const tbody = $('#automationTestsTableBody');
    tbody.empty();

    if (tests.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-robot fa-2x mb-2"></i><br>
                    暂无自动化测试用例
                </td>
            </tr>
        `);
        return;
    }

    tests.forEach(function(test) {
        const statusBadge = getStatusBadge(test.status);
        const typeBadge = getTypeBadge(test.type);
        
        const row = $(`
            <tr>
                <td>
                    <input type="checkbox" class="test-checkbox" value="${test.id}" onchange="updateSelection()">
                </td>
                <td>
                    <a href="javascript:void(0)" onclick="viewTest('${test.id}')" class="text-decoration-none">
                        <strong>${test.name || '未命名测试'}</strong>
                    </a>
                    <br><small class="text-muted">${test.description || '暂无描述'}</small>
                </td>
                <td><span class="badge bg-info">${test.framework || 'midscenejs'}</span></td>
                <td>${statusBadge}</td>
                <td>
                    ${test.user_test_case_id ?
                        `<a href="javascript:void(0)" onclick="showUserTestCaseDetails('${test.user_test_case_id}')" class="text-decoration-none">
                            <small>${test.user_test_case_name || '关联用例'}</small>
                        </a>` :
                        '<small class="text-muted">无关联用例</small>'
                    }
                </td>
                <td>
                    <small class="text-muted">${test.last_executed ? formatDate(test.last_executed) : '未执行'}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewTest('${test.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="executeTest('${test.id}')" title="执行测试">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editTest('${test.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteTest('${test.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 其他函数（状态徽章、类型徽章、日期格式化等）
function getStatusBadge(status) {
    const badges = {
        'draft': '<span class="badge bg-secondary">草稿</span>',
        'ready': '<span class="badge bg-primary">就绪</span>',
        'running': '<span class="badge bg-warning">运行中</span>',
        'passed': '<span class="badge bg-success">通过</span>',
        'failed': '<span class="badge bg-danger">失败</span>'
    };
    return badges[status] || '<span class="badge bg-light text-dark">未知</span>';
}

function getTypeBadge(type) {
    const badges = {
        'unit': '<span class="badge bg-info">单元测试</span>',
        'integration': '<span class="badge bg-warning">集成测试</span>',
        'api': '<span class="badge bg-success">API测试</span>',
        'ui': '<span class="badge bg-primary">UI测试</span>'
    };
    return badges[type] || '<span class="badge bg-light text-dark">其他</span>';
}

function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 刷新列表
function refreshTests() {
    loadAutomationTests();
}

// 改变分页大小
function changePageSize() {
    currentPage = 1;
    loadAutomationTests();
}

// 其他功能函数（待实现）
function showGenerateModal() {
    showNotification('自动化测试生成功能正在开发中...', 'info');
}

function showCreateModal() {
    // 显示创建自动化测试用例模态框
    $('#createModal').modal('show');
}

function generateAutomationTests() {
    const selectedRequirements = $('#requirementSelect').val();
    if (!selectedRequirements || selectedRequirements.length === 0) {
        showNotification('请选择至少一个需求功能点', 'warning');
        return;
    }

    const automationTestType = $('#automationTestType').val();
    const includeSetup = $('#includeSetup').is(':checked');
    const includeTeardown = $('#includeTeardown').is(':checked');

    const data = {
        requirement_ids: selectedRequirements,
        automation_test_type: automationTestType,
        include_setup: includeSetup,
        include_teardown: includeTeardown
    };

    showProgress('正在生成自动化测试用例，请稍候...');
    bootstrap.Modal.getInstance($('#generateModal')[0]).hide();

    $.ajax({
        url: '/aitest/tests/api/generate_automation',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        timeout: 300000, // 5分钟超时
        success: function(response) {
            hideProgress();
            if (response && response.success) {
                const message = response.message || '自动化测试用例生成成功';
                showNotification(message, 'success');
                loadAutomationTests();
            } else {
                const errorMessage = response?.message || '生成自动化测试用例失败';
                showNotification(errorMessage, 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            console.error('生成自动化测试用例错误:', xhr, status, error);
            let errorMessage = '生成自动化测试用例失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (status === 'timeout') {
                errorMessage = '生成超时，请稍后重试';
            }
            showNotification(errorMessage, 'error');
        }
    });
}

function toggleSelectAll() {
    const headerCheckbox = $('#headerCheckbox');
    const isChecked = headerCheckbox.is(':checked');

    $('.test-checkbox').prop('checked', isChecked);
    updateSelection();
}

function updateSelection() {
    selectedTests.clear();
    $('.test-checkbox:checked').each(function() {
        selectedTests.add($(this).val());
    });

    // 更新批量操作按钮状态
    if (selectedTests.size > 0) {
        $('#batchActions').show();
    } else {
        $('#batchActions').hide();
    }

    // 更新全选框状态
    const totalCheckboxes = $('.test-checkbox').length;
    const checkedCheckboxes = $('.test-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#headerCheckbox').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#headerCheckbox').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#headerCheckbox').prop('indeterminate', true);
    }
}

function batchDelete() {
    if (selectedTests.size === 0) {
        showNotification('请选择要删除的自动化测试用例', 'warning');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedTests.size} 个自动化测试用例吗？`)) {
        return;
    }

    const data = {
        case_ids: Array.from(selectedTests)
    };

    $.ajax({
        url: '/aitest/tests/api/automation/batch_delete',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response && response.success) {
                showNotification(response.message || '批量删除成功', 'success');
                selectedTests.clear();
                loadAutomationTests();
            } else {
                showNotification(response?.message || '批量删除失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('批量删除错误:', xhr, status, error);
            showNotification('批量删除失败', 'error');
        }
    });
}

function batchExecute() {
    if (selectedTests.size === 0) {
        showNotification('请选择要执行的自动化测试用例', 'warning');
        return;
    }

    if (!confirm(`确定要执行选中的 ${selectedTests.size} 个自动化测试用例吗？`)) {
        return;
    }

    const data = {
        case_ids: Array.from(selectedTests)
    };

    showProgress('正在执行自动化测试用例，请稍候...');

    $.ajax({
        url: '/aitest/tests/api/automation/batch_execute',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            hideProgress();
            if (response && response.success) {
                showNotification(response.message || '批量执行已启动', 'success');
                selectedTests.clear();
                loadAutomationTests();
            } else {
                showNotification(response?.message || '批量执行失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            console.error('批量执行错误:', xhr, status, error);
            showNotification('批量执行失败', 'error');
        }
    });
}

function viewTest(testId) {
    // 跳转到自动化测试用例详情页面
    window.location.href = `/aitest/tests/automation/${testId}`;
}

function editTest(testId) {
    // 获取自动化测试用例详情并显示编辑模态框
    $.get(`/aitest/tests/api/automation/${testId}`, function(response) {
        if (response.success) {
            const testCase = response.data;
            showEditModal(testCase);
        } else {
            showNotification('获取自动化测试用例详情失败', 'error');
        }
    }).fail(function() {
        showNotification('获取自动化测试用例详情失败', 'error');
    });
}

function deleteTest(testId) {
    if (!confirm('确定要删除这个自动化测试用例吗？')) {
        return;
    }

    $.ajax({
        url: `/aitest/tests/api/automation/${testId}/delete`,
        method: 'DELETE',
        success: function(response) {
            if (response && response.success) {
                showNotification(response.message || '删除成功', 'success');
                loadAutomationTests();
            } else {
                showNotification(response?.message || '删除失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('删除错误:', xhr, status, error);
            showNotification('删除失败', 'error');
        }
    });
}

function executeTest(testId) {
    showNotification('执行测试功能正在开发中...', 'info');
}

// 刷新测试列表
function refreshTests() {
    currentPage = 1;
    selectedTests.clear();
    $('#batchActions').hide();
    $('#headerCheckbox').prop('checked', false).prop('indeterminate', false);
    loadAutomationTests();
}

function loadRequirements() {
    $.get('/aitest/requirements/api/list', { page_size: 1000 }, function(response) {
        if (response.success) {
            const select = $('#requirementSelect');
            select.empty();

            response.data.records.forEach(function(req) {
                select.append(`<option value="${req.id}">${req.title || req.name}</option>`);
            });
        }
    });
}

// 加载用户测试用例列表
function loadUserTestCases() {
    $.get('/aitest/tests/api/list', { page_size: 1000, type: 'functional' }, function(response) {
        if (response.success) {
            const createSelect = $('#createUserTestCase');
            const editSelect = $('#editUserTestCase');

            // 清空现有选项，保留"无关联用例"选项
            createSelect.find('option:not(:first)').remove();
            editSelect.find('option:not(:first)').remove();

            response.data.records.forEach(function(testCase) {
                const option = `<option value="${testCase.id}">${testCase.name}</option>`;
                createSelect.append(option);
                editSelect.append(option);
            });
        }
    });
}

function showUserTestCaseDetails(userTestCaseId) {
    // 获取用户测试用例详情
    $.get(`/aitest/tests/api/${userTestCaseId}`, function(response) {
        if (response.success) {
            const testCase = response.data;
            const modalContent = `
                <div class="modal fade" id="userTestCaseModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">用户测试用例详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>用例名称:</strong> ${testCase.name || '未命名'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>状态:</strong> ${getStatusBadge(testCase.status)}
                                    </div>
                                </div>
                                <hr>
                                <div class="mb-3">
                                    <strong>描述:</strong><br>
                                    ${testCase.description || '暂无描述'}
                                </div>
                                <div class="mb-3">
                                    <strong>测试步骤:</strong><br>
                                    <pre class="bg-light p-2">${testCase.steps || '暂无步骤'}</pre>
                                </div>
                                <div class="mb-3">
                                    <strong>预期结果:</strong><br>
                                    <pre class="bg-light p-2">${testCase.expected_result || '暂无预期结果'}</pre>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            $('#userTestCaseModal').remove();

            // 添加新的模态框并显示
            $('body').append(modalContent);
            const modal = new bootstrap.Modal($('#userTestCaseModal')[0]);
            modal.show();
        } else {
            showNotification('获取用户测试用例详情失败', 'error');
        }
    }).fail(function() {
        showNotification('获取用户测试用例详情失败', 'error');
    });
}

// 创建自动化测试用例
function createAutomationTest() {
    const name = $('#createName').val().trim();
    const description = $('#createDescription').val().trim();
    const framework = $('#createFramework').val();
    const userTestCaseId = $('#createUserTestCase').val();
    const script = $('#createScript').val().trim();

    if (!name) {
        showNotification('请输入测试用例名称', 'warning');
        return;
    }

    if (!script) {
        showNotification('请输入测试脚本', 'warning');
        return;
    }

    const data = {
        name: name,
        description: description,
        framework: framework,
        user_test_case_id: userTestCaseId || null,
        test_script: script
    };

    $.ajax({
        url: '/aitest/tests/api/automation/create',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response && response.success) {
                showNotification(response.message || '创建成功', 'success');
                bootstrap.Modal.getInstance($('#createModal')[0]).hide();
                $('#createForm')[0].reset();
                loadAutomationTests();
            } else {
                showNotification(response?.message || '创建失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('创建错误:', xhr, status, error);
            showNotification('创建失败', 'error');
        }
    });
}

// 显示编辑模态框
function showEditModal(testCase) {
    $('#editId').val(testCase.id);
    $('#editName').val(testCase.name || '');
    $('#editDescription').val(testCase.description || '');
    $('#editFramework').val(testCase.framework || 'midscenejs');
    $('#editUserTestCase').val(testCase.user_test_case_id || '');
    $('#editScript').val(testCase.test_script || '');

    $('#editModal').modal('show');
}

// 更新自动化测试用例
function updateAutomationTest() {
    const id = $('#editId').val();
    const name = $('#editName').val().trim();
    const description = $('#editDescription').val().trim();
    const framework = $('#editFramework').val();
    const userTestCaseId = $('#editUserTestCase').val();
    const script = $('#editScript').val().trim();

    if (!name) {
        showNotification('请输入测试用例名称', 'warning');
        return;
    }

    if (!script) {
        showNotification('请输入测试脚本', 'warning');
        return;
    }

    const data = {
        name: name,
        description: description,
        framework: framework,
        user_test_case_id: userTestCaseId || null,
        test_script: script
    };

    $.ajax({
        url: `/aitest/tests/api/automation/${id}/update`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response && response.success) {
                showNotification(response.message || '更新成功', 'success');
                bootstrap.Modal.getInstance($('#editModal')[0]).hide();
                loadAutomationTests();
            } else {
                showNotification(response?.message || '更新失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('更新错误:', xhr, status, error);
            showNotification('更新失败', 'error');
        }
    });
}
</script>
{% endblock %}
