# AI智能测试平台 (IntelliTest)

一个基于大语言模型的智能测试用例生成平台，支持从需求文档自动提取功能点并生成测试用例。

## 🚀 功能特性

### 核心功能
- **项目管理**: 创建和管理测试项目，支持项目统计和数据导出
- **文档管理**: 上传和管理需求文档，支持Word、PDF、Markdown等格式
- **需求提取**: 使用大模型从文档中智能提取需求功能点
- **知识库管理**: 基于向量数据库的知识检索和管理
- **测试用例生成**: 根据需求自动生成用户测试用例和自动化测试用例
- **系统配置**: 灵活的大模型API配置和提示词管理

### 技术特点
- **模块化架构**: 清晰的分层设计，易于扩展和维护
- **多存储支持**: 支持JSON文件和SQLite数据库存储
- **智能解析**: 集成大语言模型进行文档理解和需求提取
- **向量检索**: 基于ChromaDB的语义搜索和知识管理
- **Web界面**: 现代化的响应式Web界面

## 📁 项目结构

```
intelliTest/
├── src/                          # 核心源代码
│   ├── config.py                 # 配置管理
│   ├── storage.py                # 数据存储层
│   ├── project_manager.py        # 项目管理
│   ├── document_manager.py       # 文档管理
│   ├── requirement_manager.py    # 需求管理
│   ├── knowledge_manager.py      # 知识库管理
│   ├── test_manager.py           # 测试管理
│   ├── system_config.py          # 系统配置
│   ├── utils/                    # 工具模块
│   │   ├── llm_client.py         # 大模型客户端
│   │   ├── embedding_client.py   # 嵌入模型客户端
│   │   └── file_converter.py     # 文件格式转换
│   └── routes/                   # Web路由
│       ├── project_routes.py     # 项目路由
│       ├── document_routes.py    # 文档路由
│       ├── requirement_routes.py # 需求路由
│       ├── knowledge_routes.py   # 知识库路由
│       ├── test_routes.py        # 测试路由
│       └── system_routes.py      # 系统路由
├── templates/                    # HTML模板
│   ├── base.html                 # 基础模板
│   ├── index.html                # 主页
│   ├── projects/                 # 项目管理页面
│   ├── documents/                # 文档管理页面
│   ├── requirements/             # 需求管理页面
│   ├── knowledge/                # 知识库页面
│   ├── tests/                    # 测试管理页面
│   └── system/                   # 系统配置页面
├── static/                       # 静态资源
│   ├── css/style.css             # 样式文件
│   └── js/common.js              # 通用JavaScript
├── data/                         # 数据存储目录
├── uploads/                      # 文件上传目录
├── knowledge_base/               # 知识库存储
├── debug/                        # 调试和测试脚本
│   ├── test_basic_functionality.py  # 基础功能测试
│   ├── test_web_app.py              # Web应用测试
│   ├── create_test_data.py          # 测试数据生成
│   └── test_report.md               # 测试报告
├── app.py                        # Flask应用入口
├── requirements.txt              # Python依赖
├── .env.example                  # 环境变量示例
└── README.md                     # 项目说明
```

## 🛠️ 安装和配置

### 1. 环境要求
- Python 3.8+
- Flask 2.0+
- 其他依赖见 requirements.txt

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 环境配置
复制 `.env.example` 为 `.env` 并配置相关参数：

```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# 大模型配置
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# 嵌入模型配置
EMBEDDING_API_BASE=https://api.openai.com/v1
EMBEDDING_API_KEY=your_api_key_here
EMBEDDING_MODEL=text-embedding-ada-002

# 存储配置
STORAGE_TYPE=json
DATA_PATH=./data
UPLOAD_FOLDER=./uploads
KNOWLEDGE_BASE_PATH=./knowledge_base

# 知识库配置
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
```

### 4. 启动应用
```bash
python app.py
```

访问 http://127.0.0.1:5000 查看应用。

## 🧪 测试

### 运行基础功能测试
```bash
python debug/test_basic_functionality.py
```

### 创建测试数据
```bash
python debug/create_test_data.py
```

### 启动测试Web应用
```bash
python debug/test_web_app.py
```

## 📖 使用指南

### 1. 创建项目
1. 访问"项目管理"页面
2. 点击"创建项目"按钮
3. 填写项目名称和描述
4. 在顶部导航栏选择创建的项目

### 2. 上传文档
1. 在"文档管理"页面点击"上传文档"
2. 选择Word、PDF或Markdown文件
3. 填写文档名称和描述
4. 上传后可预览文档内容

### 3. 提取需求
1. 在"需求管理"页面点击"提取需求"
2. 选择已上传的文档
3. 系统将自动提取需求功能点
4. 确认后保存到数据库

### 4. 生成测试用例
1. 在需求列表中选择需求功能点
2. 点击"生成测试用例"按钮
3. 系统将自动生成用户测试用例
4. 可进一步生成自动化测试用例

### 5. 知识库管理
1. 在"知识库管理"页面上传知识文档
2. 或使用"自动导入文档"功能
3. 使用搜索功能检索相关知识
4. 测试知识库检索效果

## 🔧 配置说明

### 大模型配置
- 支持OpenAI API兼容的服务
- 可配置不同的模型和参数
- 支持自定义提示词模板

### 存储配置
- JSON文件存储：适合开发和小规模使用
- SQLite数据库：适合生产环境
- 支持数据导入导出

### 知识库配置
- 基于ChromaDB向量数据库
- 可配置文本分块大小和重叠
- 支持语义搜索和相似度匹配

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如有问题或建议，请：
1. 查看 [debug/test_report.md](debug/test_report.md) 了解已知问题
2. 提交 Issue 描述问题
3. 参考测试脚本进行调试

## 🎯 路线图

- [ ] 完善Web界面交互
- [ ] 添加用户认证和权限管理
- [ ] 支持更多文档格式
- [ ] 集成更多大模型服务
- [ ] 添加测试执行和报告功能
- [ ] 支持团队协作功能

---

**开发状态**: ✅ 基础功能完成  
**测试状态**: ✅ 核心功能测试通过  
**部署状态**: 🔄 开发环境就绪

## Docker环境启动

```bash
# 构建镜像
docker build -t ai-test:0821 .

docker run -d --name aitest -v $(pwd)/data:/aitest/data -p 5000:5000 ai-test:0821

# 还可以直接映射项目目录，方便调试
docker run  -d --name aitest -v $(pwd):/aitest -p 5000:5000 ai-test:0821

# 前端运行
docker run -ti --rm --name aitest -v $(pwd):/aitest -p 5000:5000 ai-test:0821

# 内部运行调试
docker run -d --name aitest -v $(pwd):/aitest -p 5000:5000 ai-test:0821 sleep inf

# 镜像导出
docker save ai-test:0821 > ai-test-0821.tar
# 镜像导入
docker load < ai-test-0821.tar
```