#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础功能测试脚本
测试AI智能测试平台的核心功能
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.config import Config
from src.storage import StorageManager
from src.utils.llm_client import LLMClient
from src.requirement_manager import RequirementManager
from src.test_manager import TestManager

def test_requirement_extract():
    """测试LLM客户端"""
    print("\n" + "=" * 50)
    print("测试LLM客户端")
    print("=" * 50)
    
    try:
        config = Config()
        llm_client = LLMClient(config)
        storage = StorageManager(config)
        req_manager = RequirementManager(storage, config)
                
        # 测试文本解析功能
        test_text = """
        ## 6.1. 产品运行环境

软件环境：  

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0001</td><td>操作系统</td><td>Ubuntu 22.04.4 LTS</td><td>高</td></tr><tr><td>PR-E-0002</td><td>JAVA库</td><td>OpenJDK 17</td><td>高</td></tr><tr><td>PR-E-0003</td><td>Nginx</td><td>sansec/3.20.2</td><td>高</td></tr><tr><td></td><td></td><td>使用公用组件JCE-5.3.3.22版本； 监控组件使用2.1版本？？</td><td>高</td></tr><tr><td></td><td>Python</td><td>python3.10+</td><td></td></tr><tr><td></td><td>Go</td><td></td><td></td></tr></table></body></html>

硬件配置：

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0004</td><td>AI一体机整 体配置</td><td>CPU: Intel Xeon Gold 6430 32核64高 线程*2 内存：32GB DDR4 RECC*8 硬盘1：480GSATA*2 硬盘2：3.84TNVME*2 GPU: GeForce RTX 4090 24GB *4 电源：2000W*2 Ukey : XT200*3</td><td></td></tr></table></body></html>
        ## 6.4. 监控组件

6.4.1. 监控组件适配

### PR-F-1001 监控组件安装适配

<html><body><table><tr><td>需求编号</td><td>PR-F-1001</td><td>需求名称</td><td>监控组件安装适配</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1、需要在AI一体机上安装监控组件以及所需的基础运行环境； 2、保证监控组件在AI一体机上稳定运行； 3、提供可监控指标的汇总表。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>輸入輸出約束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">监控组件安装成功，并可稳定运行</td></tr></table></body></html>

<html><body><table><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

### PR-F-1002 监控组件功能测试

<html><body><table><tr><td>需求编号</td><td>PR-F-1002</td><td>需求名称</td><td>监控组件功能测试</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.测试监控组件是否可以正确获取各项指标； 2.整理指标集，确定哪些指标需要在管理系统上展示，对外输出指标汇总表；</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">1、输出指标汇总表； 2、监控组件各指标采集正常；</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

        """
        
        system_prompt = req_manager.load_requirement_prompt()
        requirements = llm_client.extract_requirements(test_text, system_prompt)
        print(f"✓ 文本需求解析成功: {len(requirements)}个需求")
        
        for req in requirements:
            #print(f"  - {req.get('number', 'N/A')}: {req.get('description', 'N/A')}")
            print(f"  - {req}")
        
        return True
    except Exception as e:
        print(f"✗ LLM客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_make_case():
    """测试用例生成功能"""
    print("\n" + "=" * 50)
    print("测试用例生成")
    print("=" * 50)
    
    try:
        config = Config()
        llm_client = LLMClient(config)
        storage = StorageManager(config)
        req_manager = RequirementManager(storage, config)
        test_manager = TestManager(storage, config)
        
        
        # 创建测试项目
        requirements = [{'number': 'PR-F-1001', 'title': '监控组件安装适配', 'section': '6.4.1. 监控组件适配', 'type': '功能需求', 'description': '系统需要在AI一体机上安装监控组件及其所需的基础运行环境，确保监控组件在AI一体机上稳定运行，并提供可监控指标的汇总表。', 'priority': '高'},
                         {'number': 'PR-F-1101', 'title': 'AI一体机特殊指标整理', 'section': '6.4.2. 监控组件功能扩展', 'type': '功能需求', 'description': '收集AI一体机上需要单独采集的指标，包括显卡数量、显存、温度、进程使用情况等，并考虑国产GPU的兼容问题，将这些指标汇总至指标汇总表。', 'priority': '高'}]
        
        system_prompt = test_manager.load_test_case_prompt()
        knowledge_context = ""
        use_cases = llm_client.generate_test_cases(requirements, knowledge_context, system_prompt)
        for use_case in use_cases:
            print(f"  - {use_case}")
        
        return True
    except Exception as e:
        print(f"✗ 测试用例生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_make_automation():
    """测试用例生成功能"""
    print("\n" + "=" * 50)
    print("自动化测试用例生成")
    print("=" * 50)
    
    try:
        config = Config()
        llm_client = LLMClient(config)
        storage = StorageManager(config)
        req_manager = RequirementManager(storage, config)
        test_manager = TestManager(storage, config)
        
        
        # 创建测试项目
        use_cases = {'name': '指标汇总表生成测试', 'type': 'functional', 'description': '验证监控指标汇总表的生成完整性和准确性', 'preconitions': '1. 监控组件已成功安装并运行\n2. AI一体机配置至少1块GPU', 'steps': ['1. 执行指标采集任务', '2. 检查生成的汇总表字段', '3. 验证GPU数量、显存等关键指标值', '4. 核对汇总表更新时间戳'], 'expected_result': '1. 汇总表包含所有预定义指标字段\n2. GPU数量与实际硬件配置一致\n3. 更新时间戳为当前时间', 'priority': '高'}
        
        system_prompt = test_manager.load_automation_prompt()
        knowledge_context = ""
        # use_cases = llmclient.generate_automation_test_cases(use_cases, system_prompt)
        project_id = "56ff9433-a18f-403b-be0a-b52938b2707b"
        test_case_ids = ["dea3976a-7f85-40a3-a93a-8d90a1b7e424", "31909672-344b-44a7-b502-3770741d1958"]
        use_cases = test_manager.generate_automation_test_cases(project_id, test_case_ids)
        for use_case in use_cases.get("automation_cases",[]):
            print(f"  - {use_case}")
        
        return True
    except Exception as e:
        print(f"✗ 自动化测试用例生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False
def main():
    """主测试函数"""
    test_results = []
    
    # 运行各项测试
    #test_results.append(("需求功能提取", test_requirement_extract()))
    #test_results.append(("测试用例生成", test_make_case()))
    test_results.append(("自动化测试用例生成", test_make_automation()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed + failed}个测试, {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("\n🎉 所有测试通过！系统基础功能正常。")
    else:
        print(f"\n⚠️  有{failed}个测试失败，请检查相关模块。")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
