#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, render_template, session
import json
from ..requirement_manager import RequirementManager

def create_requirement_blueprint(requirement_manager:RequirementManager):
    """创建需求路由蓝图"""
    bp = Blueprint('requirements', __name__, url_prefix='/aitest/requirements')
    
    @bp.route('/')
    def index():
        """需求管理页面"""
        return render_template('requirements/index.html')
    
    @bp.route('/api/list')
    def list_requirements():
        """获取需求列表API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        search = request.args.get('search', '').strip()
        
        # 过滤条件
        filters = {}
        if request.args.get('type'):
            filters['type'] = request.args.get('type')
        if request.args.get('priority'):
            filters['priority'] = request.args.get('priority')
        if request.args.get('status'):
            filters['status'] = request.args.get('status')
        if request.args.get('document_id'):
            filters['document_id'] = request.args.get('document_id')
        
        try:
            result = requirement_manager.list_requirements(
                project_id=project_id,
                page=page,
                page_size=page_size,
                filters=filters if filters else None,
                search=search if search else None
            )
            return jsonify({
                'success': True,
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取需求列表失败: {str(e)}'
            }), 500
    
    @bp.route('/api/extract', methods=['POST'])
    def extract_requirements():
        """从文档提取需求API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        document_id = data.get('document_id')
        batch_by_sections = data.get('batch_by_sections', True)
        section_separator = data.get('section_separator', '###')
        max_threads = data.get('max_threads', 4)

        if not document_id:
            return jsonify({
                'success': False,
                'message': '请选择文档'
            }), 400

        try:
            result = requirement_manager.extract_requirements_from_document(
                document_id, project_id, batch_by_sections, section_separator, max_threads
            )
            print( f'成功提取 {result["total_extracted"]} 个需求功能点')
            
            return jsonify({
                'success': True,
                'data': result,
                'message': f'成功提取 {result["total_extracted"]} 个需求功能点'
            })
        except Exception as e:
            print( f'需求提取失败: {str(e)}')
            return jsonify({
                'success': False,
                'message': f'需求提取失败: {str(e)}'
            }), 500
    
    @bp.route('/api/save_extracted', methods=['POST'])
    def save_extracted_requirements():
        """保存提取的需求API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        requirements = data.get('requirements', [])
        
        if not requirements:
            return jsonify({
                'success': False,
                'message': '没有需求数据'
            }), 400
        
        try:
            saved_ids = requirement_manager.save_extracted_requirements(requirements)
            
            return jsonify({
                'success': True,
                'data': {'saved_ids': saved_ids},
                'message': f'成功保存 {len(saved_ids)} 个需求功能点'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'保存需求失败: {str(e)}'
            }), 500
    
    @bp.route('/api/create', methods=['POST'])
    def create_requirement():
        """创建需求API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('description'):
            return jsonify({
                'success': False,
                'message': '需求描述不能为空'
            }), 400
        
        try:
            requirement_id = requirement_manager.create_requirement(project_id, data)
            
            return jsonify({
                'success': True,
                'data': {'id': requirement_id},
                'message': '需求创建成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'创建需求失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<requirement_id>')
    def get_requirement(requirement_id):
        """获取需求详情API"""
        project_id = session.get('current_project_id')
        
        try:
            requirement = requirement_manager.get_requirement(requirement_id, project_id)
            if not requirement:
                return jsonify({
                    'success': False,
                    'message': '需求不存在'
                }), 404
            
            return jsonify({
                'success': True,
                'data': requirement
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取需求详情失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<requirement_id>/update', methods=['PUT'])
    def update_requirement(requirement_id):
        """更新需求API"""
        project_id = session.get('current_project_id')
        data = request.get_json()
        
        try:
            success = requirement_manager.update_requirement(requirement_id, data, project_id)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '需求更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '需求更新失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新需求失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<requirement_id>/delete', methods=['DELETE'])
    def delete_requirement(requirement_id):
        """删除需求API"""
        project_id = session.get('current_project_id')
        
        try:
            success = requirement_manager.delete_requirement(requirement_id, project_id)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '需求删除成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '需求删除失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除需求失败: {str(e)}'
            }), 500
    
    # @bp.route('/api/batch_delete', methods=['POST'])
    # def batch_delete_requirements():
    #     """批量删除需求API"""
    #     project_id = session.get('current_project_id')
    #     if not project_id:
    #         return jsonify({
    #             'success': False,
    #             'message': '请先选择项目'
    #         }), 400
        
    #     data = request.get_json()
    #     requirement_ids = data.get('requirement_ids', [])
        
    #     if not requirement_ids:
    #         return jsonify({
    #             'success': False,
    #             'message': '请选择要删除的需求'
    #         }), 400
        
    #     try:
    #         result = requirement_manager.batch_delete_requirements(requirement_ids, project_id)
            
    #         return jsonify({
    #             'success': True,
    #             'data': result,
    #             'message': f'成功删除 {result["success_count"]} 个需求'
    #         })
    #     except Exception as e:
    #         return jsonify({
    #             'success': False,
    #             'message': f'批量删除失败: {str(e)}'
    #         }), 500
    
    @bp.route('/api/types')
    def get_requirement_types():
        """获取需求类型列表API"""
        try:
            types = requirement_manager.get_requirement_types()
            return jsonify({
                'success': True,
                'data': types
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取需求类型失败: {str(e)}'
            }), 500
    
    @bp.route('/api/priorities')
    def get_requirement_priorities():
        """获取需求优先级列表API"""
        try:
            priorities = requirement_manager.get_requirement_priorities()
            return jsonify({
                'success': True,
                'data': priorities
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取需求优先级失败: {str(e)}'
            }), 500
    
    @bp.route('/api/statuses')
    def get_requirement_statuses():
        """获取需求状态列表API"""
        try:
            statuses = requirement_manager.get_requirement_statuses()
            return jsonify({
                'success': True,
                'data': statuses
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取需求状态失败: {str(e)}'
            }), 500
    
    @bp.route('/api/statistics')
    def get_requirement_statistics():
        """获取需求统计信息API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        try:
            stats = requirement_manager.get_requirement_statistics(project_id)
            return jsonify({
                'success': True,
                'data': stats
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取需求统计失败: {str(e)}'
            }), 500
    
    @bp.route('/api/batch_delete', methods=['DELETE'])
    def batch_delete_requirements():
        """批量删除需求API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        data = request.get_json()
        requirement_ids = data.get('requirement_ids', [])

        if not requirement_ids:
            return jsonify({
                'success': False,
                'message': '请选择要删除的需求'
            }), 400

        try:
            deleted_count = 0
            errors = []

            for req_id in requirement_ids:
                try:
                    success = requirement_manager.delete_requirement(req_id, project_id)
                    if success:
                        deleted_count += 1
                    else:
                        errors.append(f"需求 {req_id} 删除失败")
                except Exception as e:
                    errors.append(f"需求 {req_id} 删除失败: {str(e)}")

            if deleted_count > 0:
                message = f'成功删除 {deleted_count} 个需求'
                if errors:
                    message += f'，{len(errors)} 个失败'

                return jsonify({
                    'success': True,
                    'message': message,
                    'data': {
                        'deleted_count': deleted_count,
                        'errors': errors
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '没有需求被删除',
                    'data': {'errors': errors}
                }), 400

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'批量删除失败: {str(e)}'
            }), 500

    return bp
