{% extends "base.html" %}

{% block title %}自动化测试用例详情 - AI智能测试平台{% endblock %}

{% block extra_css %}
<!-- Highlight.js CSS for YAML syntax highlighting -->
<link rel="stylesheet" href="/aitest/static/css/github.min.css">
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/aitest">首页</a></li>
<li class="breadcrumb-item"><a href="/aitest/tests/automation">自动化测试用例</a></li>
<li class="breadcrumb-item active">测试用例详情</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-robot me-2"></i>自动化测试用例详情
            </h1>
            <div class="btn-group">
                <button class="btn btn-outline-secondary" onclick="history.back()">
                    <i class="fas fa-arrow-left me-1"></i>返回
                </button>
                <button class="btn btn-warning" onclick="editTest()">
                    <i class="fas fa-edit me-1"></i>编辑
                </button>
                <button class="btn btn-success" onclick="executeTest()">
                    <i class="fas fa-play me-1"></i>执行测试
                </button>
                <button class="btn btn-danger" onclick="deleteTest()">
                    <i class="fas fa-trash me-1"></i>删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 测试用例基本信息 -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">测试用例名称</label>
                            <p id="testName" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">测试框架</label>
                            <p id="testFramework" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">状态</label>
                            <p id="testStatus" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">关联用户测试用例</label>
                            <p id="userTestCase" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">描述</label>
                    <p id="testDescription" class="form-control-plaintext">-</p>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">创建时间</label>
                            <p id="createdAt" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">更新时间</label>
                            <p id="updatedAt" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">AI生成</label>
                            <p id="aiGenerated" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">AI生成后修改时间</label>
                            <p id="aiModifiedAt" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>执行统计
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">最后执行时间</label>
                    <p id="lastExecuted" class="form-control-plaintext">-</p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">执行次数</label>
                    <p id="executionCount" class="form-control-plaintext">0</p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">成功率</label>
                    <p id="successRate" class="form-control-plaintext">-</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 测试脚本 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-code me-2"></i>测试脚本 (YAML格式)
                </h5>
            </div>
            <div class="card-body">
                <pre id="testScript" class="bg-light p-3 rounded" style="max-height: 500px; overflow-y: auto;"><code>加载中...</code></pre>
            </div>
        </div>
    </div>
</div>

<!-- 关联的用户测试用例详情 -->
<div class="row mt-4" id="userTestCaseSection" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>关联的用户测试用例
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">用例名称</label>
                            <p id="userTestCaseName" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">用例状态</label>
                            <p id="userTestCaseStatus" class="form-control-plaintext">-</p>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">用例描述</label>
                    <p id="userTestCaseDescription" class="form-control-plaintext">-</p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">测试步骤</label>
                    <pre id="userTestCaseSteps" class="bg-light p-2 rounded">-</pre>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">预期结果</label>
                    <pre id="userTestCaseExpected" class="bg-light p-2 rounded">-</pre>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑模态框 -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑自动化测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div class="mb-3">
                        <label for="editName" class="form-label">测试用例名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="editDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editFramework" class="form-label">测试框架</label>
                                <select class="form-select" id="editFramework">
                                    <option value="midscenejs">Midscene.js</option>
                                    <option value="selenium">Selenium</option>
                                    <option value="playwright">Playwright</option>
                                    <option value="cypress">Cypress</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editUserTestCase" class="form-label">关联用户测试用例</label>
                                <select class="form-select" id="editUserTestCase">
                                    <option value="">无关联用例</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editScript" class="form-label">测试脚本 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="editScript" rows="10" required></textarea>
                        <div class="form-text">请使用YAML格式编写测试脚本</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateTest()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Highlight.js JavaScript for YAML syntax highlighting -->
<link rel="stylesheet" href="/aitest/static/js/highlight.min.js">
<link rel="stylesheet" href="/aitest/static/js/yaml.min.js">
<script>
let currentTestCase = null;
const caseId = '{{ case_id }}';

$(document).ready(function() {
    loadTestCaseDetail();
    loadUserTestCases();
});

// 加载测试用例详情
function loadTestCaseDetail() {
    $.get(`/aitest/tests/api/automation/${caseId}`, function(response) {
        if (response.success) {
            currentTestCase = response.data;
            renderTestCaseDetail(currentTestCase);
        } else {
            showNotification('加载测试用例详情失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载测试用例详情失败', 'error');
    });
}

// 渲染测试用例详情
function renderTestCaseDetail(testCase) {
    $('#testName').text(testCase.name || '未命名');
    $('#testFramework').html(`<span class="badge bg-info">${testCase.framework || 'midscenejs'}</span>`);
    $('#testStatus').html(getStatusBadge(testCase.status));
    $('#testDescription').text(testCase.description || '暂无描述');
    $('#createdAt').text(formatDate(testCase.created_at));
    $('#updatedAt').text(formatDate(testCase.updated_at));
    $('#aiGenerated').html(testCase.ai_generated ? '<span class="badge bg-success">是</span>' : '<span class="badge bg-secondary">否</span>');
    $('#aiModifiedAt').text(testCase.ai_modified_at ? formatDate(testCase.ai_modified_at) : '未修改');
    $('#lastExecuted').text(testCase.last_executed ? formatDate(testCase.last_executed) : '未执行');
    $('#executionCount').text(testCase.execution_count || 0);
    $('#successRate').text(testCase.success_rate ? `${testCase.success_rate}%` : '-');
    
    // 显示测试脚本
    if (testCase.test_script ) {
        const scriptElement = $(`<code class="language-yaml">${escapeHtml(testCase.test_script)}</code>`);
        $('#testScript').empty().append(scriptElement);
        // 应用语法高亮
        if (typeof hljs !== 'undefined') {
            hljs.highlightElement(scriptElement[0]);
        }
    } else {
        $('#testScript').html('<code class="text-muted">暂无测试脚本</code>');
    }
    
    // 显示关联的用户测试用例
    if (testCase.user_test_case) {
        const userCase = testCase.user_test_case;
        $('#userTestCaseName').text(userCase.name || '未命名');
        $('#userTestCaseStatus').html(getStatusBadge(userCase.status));
        $('#userTestCaseDescription').text(userCase.description || '暂无描述');
        $('#userTestCaseSteps').text(userCase.steps || '暂无步骤');
        $('#userTestCaseExpected').text(userCase.expected_result || '暂无预期结果');
        $('#userTestCaseSection').show();
        $('#userTestCase').html(`<a href="javascript:void(0)" onclick="showUserTestCaseDetails('${userCase.id}')" class="text-decoration-none">
                            <small>${userCase.name || '关联用例'}</small>
                        </a>`);
    } else {
        $('#userTestCaseSection').hide();
        $('#userTestCase').text('无关联用例');
    }
}

// 加载用户测试用例列表
function loadUserTestCases() {
    $.get('/aitest/tests/api/list', { page_size: 1000, type: 'functional' }, function(response) {
        if (response.success) {
            const select = $('#editUserTestCase');
            select.find('option:not(:first)').remove();
            
            response.data.records.forEach(function(testCase) {
                select.append(`<option value="${testCase.id}">${testCase.name}</option>`);
            });
        }
    });
}

// 编辑测试用例
function editTest() {
    if (!currentTestCase) return;
    
    $('#editName').val(currentTestCase.name || '');
    $('#editDescription').val(currentTestCase.description || '');
    $('#editFramework').val(currentTestCase.framework || 'midscenejs');
    $('#editUserTestCase').val(currentTestCase.user_test_case_id || '');
    if (currentTestCase.test_script) {
        $('#editScript').val(currentTestCase.test_script || '');
    }
    
    $('#editModal').modal('show');
}

// 更新测试用例
function updateTest() {
    const name = $('#editName').val().trim();
    const description = $('#editDescription').val().trim();
    const framework = $('#editFramework').val();
    const userTestCaseId = $('#editUserTestCase').val();
    const script = $('#editScript').val().trim();

    if (!name) {
        showNotification('请输入测试用例名称', 'warning');
        return;
    }

    if (!script) {
        showNotification('请输入测试脚本', 'warning');
        return;
    }

    const data = {
        name: name,
        description: description,
        framework: framework,
        user_test_case_id: userTestCaseId || null,
        test_script: script
    };

    $.ajax({
        url: `/aitest/tests/api/automation/${caseId}/update`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response && response.success) {
                showNotification(response.message || '更新成功', 'success');
                bootstrap.Modal.getInstance($('#editModal')[0]).hide();
                loadTestCaseDetail(); // 重新加载详情
            } else {
                showNotification(response?.message || '更新失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('更新错误:', xhr, status, error);
            showNotification('更新失败', 'error');
        }
    });
}

// 执行测试
function executeTest() {
    showNotification('执行测试功能正在开发中...', 'info');
}

// 删除测试用例
function deleteTest() {
    if (!confirm('确定要删除这个自动化测试用例吗？')) {
        return;
    }

    $.ajax({
        url: `/aitest/tests/api/automation/${caseId}/delete`,
        method: 'DELETE',
        success: function(response) {
            if (response && response.success) {
                showNotification(response.message || '删除成功', 'success');
                // 跳转回列表页面
                window.location.href = '/aitest/tests/automation';
            } else {
                showNotification(response?.message || '删除失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('删除错误:', xhr, status, error);
            showNotification('删除失败', 'error');
        }
    });
}

// 获取状态徽章
function getStatusBadge(status) {
    const statusMap = {
        '待执行': 'bg-secondary',
        '执行中': 'bg-primary',
        '通过': 'bg-success',
        '失败': 'bg-danger',
        '跳过': 'bg-warning'
    };
    
    const badgeClass = statusMap[status] || 'bg-secondary';
    return `<span class="badge ${badgeClass}">${status || '待执行'}</span>`;
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// HTML转义
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}
function showUserTestCaseDetails(userTestCaseId) {
    // 获取用户测试用例详情
    $.get(`/aitest/tests/api/${userTestCaseId}`, function(response) {
        if (response.success) {
            const testCase = response.data;
            const modalContent = `
                <div class="modal fade" id="userTestCaseModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">用户测试用例详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>用例名称:</strong> ${testCase.name || '未命名'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>状态:</strong> ${getStatusBadge(testCase.status)}
                                    </div>
                                </div>
                                <hr>
                                <div class="mb-3">
                                    <strong>描述:</strong><br>
                                    ${testCase.description || '暂无描述'}
                                </div>
                                <div class="mb-3">
                                    <strong>测试步骤:</strong><br>
                                    <pre class="bg-light p-2">${testCase.steps || '暂无步骤'}</pre>
                                </div>
                                <div class="mb-3">
                                    <strong>预期结果:</strong><br>
                                    <pre class="bg-light p-2">${testCase.expected_result || '暂无预期结果'}</pre>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            $('#userTestCaseModal').remove();

            // 添加新的模态框并显示
            $('body').append(modalContent);
            const modal = new bootstrap.Modal($('#userTestCaseModal')[0]);
            modal.show();
        } else {
            showNotification('获取用户测试用例详情失败', 'error');
        }
    }).fail(function() {
        showNotification('获取用户测试用例详情失败', 'error');
    });
}
</script>
{% endblock %}
